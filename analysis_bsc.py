import pandas as pd
import logging
from token_constants_bsc import EXCLUDED_TOKENS
from datetime import date, timedelta, datetime
import numpy as np


def calculate_unrealized_pnl_30_days(remaining_balance, avg_buy_price, price_history, current_price, total_bought=None):
    """
    Calculate unrealized profit/loss for remaining token balance over the past 30 days.
    For tokens with no current balance, calculates what the P/L would have been if tokens were still held.

    Args:
        remaining_balance (float): Current token balance held
        avg_buy_price (float): Average price at which tokens were bought
        price_history (dict): Dictionary with dates as keys and prices as values
        current_price (float): Current token price
        total_bought (float, optional): Total amount of tokens bought (used for zero balance calculations)

    Returns:
        dict: Dictionary containing unrealized P/L metrics and price data:
              - 'unrealized_pnl_current': Current unrealized P/L
              - 'unrealized_pnl_day_1' through 'unrealized_pnl_day_30': Daily P/L values for each of the past 30 days
              - 'price_day_1' through 'price_day_30': Daily price values for each of the past 30 days
    """
    # For tokens with no current balance, use total_bought to calculate hypothetical P/L
    calculation_balance = remaining_balance
    if remaining_balance <= 0 and total_bought and total_bought > 0 and avg_buy_price > 0:
        calculation_balance = total_bought

    if calculation_balance <= 0 or avg_buy_price <= 0:
        # Return zero values for current and all 30 days
        result = {'unrealized_pnl_current': 0}
        for day in range(1, 31):
            result[f'unrealized_pnl_day_{day}'] = 0
            result[f'price_day_{day}'] = 0
        return result

    # Calculate current unrealized P/L
    cost_basis = calculation_balance * avg_buy_price
    current_value = calculation_balance * current_price if current_price else 0
    unrealized_pnl_current = current_value - cost_basis

    # Initialize result with current P/L
    result = {'unrealized_pnl_current': unrealized_pnl_current}

    # Calculate daily unrealized P/L and store prices if price history is available
    if price_history:
        # Sort dates to ensure consistent ordering (most recent first)
        sorted_dates = sorted(price_history.keys(), reverse=True)

        # Calculate P/L and store prices for each of the past 30 days
        for day in range(1, 31):
            if day <= len(sorted_dates):
                # Use the price from the corresponding day
                date_key = sorted_dates[day - 1]
                price = price_history[date_key]
                if price is not None:
                    historical_value = calculation_balance * price
                    historical_pnl = historical_value - cost_basis
                    result[f'unrealized_pnl_day_{day}'] = historical_pnl
                    result[f'price_day_{day}'] = price
                else:
                    result[f'unrealized_pnl_day_{day}'] = 0
                    result[f'price_day_{day}'] = 0
            else:
                # No data available for this day, use 0
                result[f'unrealized_pnl_day_{day}'] = 0
                result[f'price_day_{day}'] = 0
    else:
        # No price history available, set all daily values to current P/L and current price
        for day in range(1, 31):
            result[f'unrealized_pnl_day_{day}'] = unrealized_pnl_current
            result[f'price_day_{day}'] = current_price if current_price else 0

    return result

def calculate_token_metrics_bsc(munged_swaps_df, token_id, token_address, ath_data, coingecko_client=None, genesis_date=None):
    """
    Calculate metrics and metadata for a BSC token based on its swaps.

    Args:
        munged_swaps_df (DataFrame): DataFrame of processed swaps for a token.
                                     Must contain 'date' (as datetime objects),
                                     'action', 'amount', 'price'.
        token_id (str): Token ID (CoinGecko ID)
        token_address (str): Token contract address
        ath_data (dict): Dictionary containing ATH data (expects 'ath_price', 'ath_date')
        coingecko_client: CoinGecko client instance for fetching price history (optional)
        genesis_date (str): Token genesis date in 'YYYY-MM-DD' format (optional)

    Returns:
        dict: Dictionary containing calculated metrics and metadata
    """
    # Sort DataFrame by date - Assumes 'date' column exists and is datetime
    try:
        munged_swaps_df = munged_swaps_df.sort_values(by='date').copy()
    except TypeError as e:
        logging.error(f"Error sorting by 'date' column (is it datetime?): {e}. Metrics requiring order might be inaccurate.")
        # Continue without sorting if it fails, though downstream calcs might also fail if 'date' is not datetime

    # Filter buy and sell actions
    buy_swaps = munged_swaps_df[munged_swaps_df["action"] == "buy"]
    sell_swaps = munged_swaps_df[munged_swaps_df["action"] == "sell"]
    
    # Calculate totals
    total_bought = buy_swaps["amount"].sum() if not buy_swaps.empty else 0
    total_sold = sell_swaps["amount"].sum() if not sell_swaps.empty else 0
    total_cost = (buy_swaps["amount"] * buy_swaps["price"]).sum() if not buy_swaps.empty else 0
    total_revenue = (sell_swaps["amount"] * sell_swaps["price"]).sum() if not sell_swaps.empty else 0
    
    remaining_balance = total_bought - total_sold
    
    # Calculate average prices using total amounts and costs/revenue
    avg_buy_price = total_cost / total_bought if total_bought > 0 else 0
    avg_sell_price = total_revenue / total_sold if total_sold > 0 else 0

    # Calculate realized profit/loss
    realized_pnl = total_revenue - (total_sold * avg_buy_price) if avg_buy_price > 0 else 0
    
    # Calculate percentage return
    percentage_return = (realized_pnl / total_cost) * 100 if total_cost > 0 else 0

    # --- Hypothetical P/L Calculation ---
    hypothetical_pnl = None
    # Calculate regardless of transaction count - only need buy/sell data
    if not munged_swaps_df.empty:
        current_holdings = 0
        max_holdings = 0
        min_holdings_after_first_buy = np.inf
        first_buy_price = None
        last_sell_price = None
        first_buy_occurred = False

        for _, row in munged_swaps_df.iterrows():
            if row["action"] == "buy":
                if first_buy_price is None:
                    first_buy_price = row["price"]
                    first_buy_occurred = True
                current_holdings += row["amount"]
                max_holdings = max(max_holdings, current_holdings)
                # Start tracking min holdings only after the first buy
                if first_buy_occurred:
                    # Track minimum including the state *after* the current buy
                    min_holdings_after_first_buy = min(min_holdings_after_first_buy, current_holdings)
            elif row["action"] == "sell":
                last_sell_price = row["price"]
                # Track minimum including the state *before* the current sell
                if first_buy_occurred:
                    min_holdings_after_first_buy = min(min_holdings_after_first_buy, current_holdings)
                current_holdings -= row["amount"]
                # Ensure holdings don't go negative due to potential data issues
                current_holdings = max(0, current_holdings)
                # Track minimum also *after* the sell
                if first_buy_occurred:
                    min_holdings_after_first_buy = min(min_holdings_after_first_buy, current_holdings)

        # Ensure min_holdings is not infinity if buys occurred but no minimum was found otherwise (e.g., only buys)
        if first_buy_occurred and min_holdings_after_first_buy == np.inf:
            # If only buys, the minimum holding reached after the first buy is the final holding.
            temp_holdings = 0
            min_holdings_calc = np.inf
            for _, row in munged_swaps_df[munged_swaps_df['action'] == 'buy'].iterrows():
                temp_holdings += row['amount']
                min_holdings_calc = min(min_holdings_calc, temp_holdings)
            min_holdings_after_first_buy = min_holdings_calc if min_holdings_calc != np.inf else 0

        # Calculate hypothetical P/L if we have buy data
        if first_buy_price is not None and max_holdings > 0:
            if last_sell_price is not None and max_holdings > min_holdings_after_first_buy:
                # Case: Both buys and sells occurred
                # Ensure min_holdings is not negative (should be handled by max(0, current_holdings))
                min_holdings_after_first_buy = max(0, min_holdings_after_first_buy)

                amount_bought_hypothetical = max_holdings
                amount_sold_hypothetical = max_holdings - min_holdings_after_first_buy

                if amount_sold_hypothetical > 0: # Need to have sold something hypothetically
                    hypothetical_cost = amount_bought_hypothetical * first_buy_price
                    hypothetical_revenue = amount_sold_hypothetical * last_sell_price
                    hypothetical_pnl = hypothetical_revenue - hypothetical_cost
                else:
                    hypothetical_pnl = 0 # If selling nothing means no hypothetical scenario
            elif last_sell_price is None:
                # Case: Bought but never sold. Calculate P/L if sold at current/ATH price
                current_price = ath_data.get("ath_price", 0)  # Use ATH as proxy for current price
                if current_price > 0:
                    hypothetical_cost = max_holdings * first_buy_price
                    hypothetical_revenue = max_holdings * current_price
                    hypothetical_pnl = hypothetical_revenue - hypothetical_cost
                else:
                    hypothetical_pnl = 0
            else:
                # Edge case: has buys and sells but max_holdings <= min_holdings
                hypothetical_pnl = 0
        # Else: hypothetical_pnl remains None if no buy transactions occurred

    # --- Hypothetical ATH Profit Calculation ---
    hypothetical_profit_ath = None
    ath_price = ath_data.get("ath_price")
    ath_date_str = ath_data.get("ath_date")

    # Calculate ATH profit only if ATH data is provided
    if ath_price is not None and ath_price > 0:
        try:
            if ath_date_str:
                # Convert ATH date string to datetime
                ath_date_dt = pd.to_datetime(ath_date_str)

                # Use transaction dates directly without timezone localization
                transaction_dates = munged_swaps_df['date']

                # Filter buys before ATH date (remove the 20% threshold requirement)
                eligible_buys = buy_swaps[transaction_dates < ath_date_dt]
            else:
                # If no ATH date, use all buy transactions
                eligible_buys = buy_swaps

            if not eligible_buys.empty:
                eligible_amount = eligible_buys["amount"].sum()
                eligible_cost = (eligible_buys["amount"] * eligible_buys["price"]).sum()

                hypothetical_revenue_ath = eligible_amount * ath_price
                hypothetical_profit_ath = hypothetical_revenue_ath - eligible_cost
            else:
                # No eligible buys, but we have ATH data - set to 0
                hypothetical_profit_ath = 0

        except Exception as e:
            logging.error(f"Error calculating hypothetical ATH profit for {token_address}: {e}")
            # If there's an error but we have ATH price, try simple calculation
            if not buy_swaps.empty:
                try:
                    total_amount = buy_swaps["amount"].sum()
                    total_cost = (buy_swaps["amount"] * buy_swaps["price"]).sum()
                    hypothetical_revenue_ath = total_amount * ath_price
                    hypothetical_profit_ath = hypothetical_revenue_ath - total_cost
                except:
                    hypothetical_profit_ath = 0
    # Else: hypothetical_profit_ath remains None only if no ATH data available

    # Check for rug pull conditions
    ath_change_percentage = ath_data.get("ath_change_percentage", None)
    is_rug = percentage_return < -50 and ath_change_percentage is not None and ath_change_percentage < -85

    # Calculate unrealized P/L for the past 30 days
    unrealized_pnl_metrics = {'unrealized_pnl_current': 0}
    # Initialize all 30 daily values to 0
    for day in range(1, 31):
        unrealized_pnl_metrics[f'unrealized_pnl_day_{day}'] = 0
        unrealized_pnl_metrics[f'price_day_{day}'] = 0

    # Calculate unrealized P/L for tokens with current balance OR tokens that were bought and sold (zero balance)
    if avg_buy_price > 0 and total_bought > 0 and coingecko_client and token_id:
        try:
            # Get current price (most recent price from history or ATH data)
            current_price = None
            price_history = None

            # Try to get 30-day price history
            if token_id != token_address:  # Only if we have a valid CoinGecko ID
                price_history = coingecko_client.get_coin_price_history_30_days(token_id)
                if price_history:
                    # Get the most recent price as current price
                    sorted_dates = sorted(price_history.keys(), reverse=True)
                    if sorted_dates:
                        current_price = price_history[sorted_dates[0]]

            # Fallback to ATH price if no current price found
            if current_price is None:
                current_price = ath_data.get("ath_price", 0)

            # Calculate unrealized P/L metrics
            # For tokens with zero balance, pass total_bought to calculate hypothetical P/L
            unrealized_pnl_metrics = calculate_unrealized_pnl_30_days(
                remaining_balance, avg_buy_price, price_history, current_price, total_bought
            )

        except Exception as e:
            logging.error(f"Error calculating unrealized P/L for {token_address}: {str(e)}")

    # Get market cap on first buy order date
    first_buy_market_cap = None
    if coingecko_client and token_id and token_id != token_address:  # Only if we have a valid CoinGecko ID
        # Find the first buy order date
        buy_orders = munged_swaps_df[munged_swaps_df["action"] == "buy"]
        if not buy_orders.empty:
            # Sort by date to get the first buy order
            first_buy_date = buy_orders.sort_values("date").iloc[0]["date"]
            # Convert datetime to date string format
            if hasattr(first_buy_date, 'strftime'):
                first_buy_date_str = first_buy_date.strftime('%Y-%m-%d')
            else:
                # If it's already a string, try to parse and reformat
                try:
                    from datetime import datetime
                    parsed_date = datetime.strptime(str(first_buy_date).split()[0], '%Y-%m-%d')
                    first_buy_date_str = parsed_date.strftime('%Y-%m-%d')
                except:
                    first_buy_date_str = str(first_buy_date).split()[0]  # Fallback

            # Fetch market cap for that date
            first_buy_market_cap = coingecko_client.get_market_cap_for_date(token_id, first_buy_date_str)

    # Build the result dictionary
    result = {
        # Metadata fields
        "token_id": token_id,
        "token_address": token_address,  # Changed from token_mint to token_address for BSC
        "genesis_date": genesis_date,
        "first_buy_market_cap": first_buy_market_cap,
        "ath_price": ath_data.get("ath_price", None),
        "ath_date": ath_data.get("ath_date", None),
        "ath_change_percentage": ath_change_percentage,

        # Metric fields
        "average_buy_price": avg_buy_price,
        "average_sell_price": avg_sell_price,
        "realized_profit_loss": realized_pnl,
        "percentage_return": percentage_return,
        "hypothetical_profit_loss": hypothetical_pnl,
        "hypothetical_profit_ath": hypothetical_profit_ath,
        "total_bought": total_bought,
        "total_sold": total_sold,
        "remaining_balance": remaining_balance,
        "rug": str(is_rug),

        # Unrealized P/L fields
        "unrealized_pnl_current": unrealized_pnl_metrics['unrealized_pnl_current'],
    }

    # Add all 30 daily unrealized P/L values and price values
    for day in range(1, 31):
        result[f'unrealized_pnl_day_{day}'] = unrealized_pnl_metrics[f'unrealized_pnl_day_{day}']
        result[f'price_day_{day}'] = unrealized_pnl_metrics[f'price_day_{day}']

    # Add transactions with proper date formatting
    transactions = munged_swaps_df.to_dict(orient='records')
    # Convert datetime objects to strings for JSON serialization
    for transaction in transactions:
        if 'date' in transaction and hasattr(transaction['date'], 'strftime'):
            transaction['date'] = transaction['date'].strftime('%Y-%m-%d %H:%M:%S')
    result["transactions"] = transactions

    return result


def analyze_tokens_from_swaps_bsc(token_id_mapping, munged_swaps_df, token_ath_data, coingecko_client=None, token_genesis_data=None):
    """
    Analyzes each BSC token based on the provided swap data.

    Args:
        token_id_mapping (dict): Mapping from token address to token ID (e.g., CoinGecko ID).
        munged_swaps_df (DataFrame): DataFrame containing all munged swap data.
        token_ath_data (dict): Dictionary containing ATH data for tokens, keyed by token address.
        coingecko_client: CoinGecko client instance for fetching price history (optional)
        token_genesis_data (dict): Dictionary containing genesis dates for tokens, keyed by token address (optional)

    Returns:
        list: A list of dictionaries, where each dictionary contains the analysis metadata for a token.
    """
    # Compute associated tokens, excluding specific ones
    # Convert excluded tokens to lowercase for comparison
    excluded_tokens_lower = {token.lower() for token in EXCLUDED_TOKENS}
    
    associated_tokens = [
        address for address in token_id_mapping.keys() 
        if address.lower() not in excluded_tokens_lower
    ]
    logging.info(f"Analyzing {len(associated_tokens)} associated BSC tokens (excluding ignored ones)")
    
    analyzed_tokens = []

    # Process each associated token
    for token_address in associated_tokens:
        # Get token ID for this address
        token_id = token_id_mapping.get(token_address)

        # Filter swaps relevant to the current token
        munged_swaps_for_token_df = munged_swaps_df[
            (munged_swaps_df["input_mint"].str.lower() == token_address.lower())
            | (munged_swaps_df["output_mint"].str.lower() == token_address.lower())
        ]

        if not munged_swaps_for_token_df.empty:
            # Get ATH data for this specific token
            ath_data = token_ath_data.get(token_address, {})

            # Get genesis date for this specific token
            genesis_date = token_genesis_data.get(token_address) if token_genesis_data else None

            # Calculate metrics and create metadata dictionary in one step
            token_metadata = calculate_token_metrics_bsc(
                munged_swaps_for_token_df, token_id, token_address, ath_data, coingecko_client, genesis_date
            )

            analyzed_tokens.append(token_metadata)
        else:
            logging.warning(f"No relevant swaps found for token {token_address} in the munged DataFrame.")
            
    return analyzed_tokens


def analyze_wallet_from_swaps_bsc(munged_swaps_df):
    """
    Calculates BSC wallet metrics based on swap transaction dates.

    Args:
        munged_swaps_df (DataFrame): DataFrame containing all munged swap data. 
                                     Requires a 'date' column.

    Returns:
        dict: A dictionary containing wallet activity metrics:
              - 'days_with_two_or_more_swaps': Count of days with >= 2 swaps.
              - 'days_with_no_swaps': Count of days with 0 swaps within the transaction range.
    """

    # Ensure 'date' is in datetime format and extract the date part
    munged_swaps_df['date'] = pd.to_datetime(munged_swaps_df['date']).dt.date

    # Group by date and count swaps per day
    daily_swap_counts = munged_swaps_df.groupby('date').size()

    # 1. Calculate the number of days with >= 2 swaps
    days_with_two_or_more_swaps = (daily_swap_counts >= 2).sum()

    # 2. Calculate the number of days with no swaps within the observed range
    min_date = munged_swaps_df['date'].min()
    max_date = munged_swaps_df['date'].max()
    
    # Create a full date range from min to max date
    all_dates = set(min_date + timedelta(days=x) for x in range((max_date - min_date).days + 1))
    
    # Get the set of dates with swaps
    dates_with_swaps = set(daily_swap_counts.index)
    
    # Find dates in the full range that are not in dates_with_swaps
    days_with_no_swaps = len(all_dates - dates_with_swaps)

    return {
        'days_with_two_or_more_swaps': days_with_two_or_more_swaps,
        'days_with_no_swaps': days_with_no_swaps
    }
